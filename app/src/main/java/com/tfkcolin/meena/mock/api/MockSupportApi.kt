package com.tfkcolin.meena.mock.api

import com.tfkcolin.meena.config.AppConfig
import com.tfkcolin.meena.data.api.*
import com.tfkcolin.meena.mock.storage.MockDataStorage
import kotlinx.coroutines.delay
import retrofit2.Response
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.random.Random

/**
 * Mock implementation of SupportApi for testing and development.
 */
@Singleton
class MockSupportApi @Inject constructor(
    private val mockDataStorage: MockDataStorage,
    private val mockAuthApi: MockAuthApi
) : SupportApi {
    
    private val mockTickets = mutableMapOf<String, Ticket>()
    private val mockTicketMessages = mutableMapOf<String, MutableList<TicketMessage>>()
    
    override suspend fun getTickets(
        limit: Int,
        offset: Int
    ): Response<TicketsResponse> {
        simulateNetworkDelay()
        
        try {
            // Generate mock tickets if none exist
            if (mockTickets.isEmpty()) {
                generateMockTickets()
            }
            
            val allTickets = mockTickets.values.sortedByDescending { it.created_at }
            val paginatedTickets = allTickets.drop(offset).take(limit)
            
            val response = TicketsResponse(
                tickets = paginatedTickets,
                total_count = allTickets.size,
                limit = limit,
                offset = offset
            )
            
            return Response.success(response)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Failed to get tickets: ${e.message}")
        }
    }
    
    override suspend fun createTicket(request: CreateTicketRequest): Response<TicketResponse> {
        simulateNetworkDelay()
        
        try {
            val ticketId = UUID.randomUUID().toString()
            val now = System.currentTimeMillis()
            
            val ticket = Ticket(
                ticket_id = ticketId,
                subject = request.subject,
                status = "open",
                priority = "medium",
                category = request.category,
                created_at = now,
                updated_at = now,
                resolved_at = null
            )
            
            mockTickets[ticketId] = ticket
            
            // Create initial message
            val initialMessage = TicketMessage(
                message_id = UUID.randomUUID().toString(),
                ticket_id = ticketId,
                sender_type = "user",
                sender_id = "current_user",
                content = request.message,
                attachments = request.attachment_ids?.map { attachmentId ->
                    TicketAttachment(
                        attachment_id = attachmentId,
                        name = "attachment_${Random.nextInt(1000, 9999)}.pdf",
                        url = "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf",
                        size = Random.nextLong(50000, 5000000),
                        mime_type = "application/pdf"
                    )
                },
                created_at = now
            )
            
            mockTicketMessages[ticketId] = mutableListOf(initialMessage)
            
            // Simulate auto-response from support
            simulateAutoResponse(ticketId)
            
            val response = TicketResponse(ticket = ticket)
            return Response.success(response)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Failed to create ticket: ${e.message}")
        }
    }
    
    override suspend fun getTicket(ticketId: String): Response<TicketResponse> {
        simulateNetworkDelay()
        
        try {
            val ticket = mockTickets[ticketId]
                ?: return createErrorResponse(404, "Ticket not found")
            
            val response = TicketResponse(ticket = ticket)
            return Response.success(response)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Failed to get ticket: ${e.message}")
        }
    }
    
    override suspend fun updateTicket(
        ticketId: String,
        request: UpdateTicketRequest
    ): Response<TicketResponse> {
        simulateNetworkDelay()
        
        try {
            val ticket = mockTickets[ticketId]
                ?: return createErrorResponse(404, "Ticket not found")
            
            val updatedTicket = ticket.copy(
                status = request.status ?: ticket.status,
                priority = request.priority ?: ticket.priority,
                updated_at = System.currentTimeMillis(),
                resolved_at = if (request.status == "resolved") System.currentTimeMillis() else ticket.resolved_at
            )
            
            mockTickets[ticketId] = updatedTicket
            
            val response = TicketResponse(ticket = updatedTicket)
            return Response.success(response)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Failed to update ticket: ${e.message}")
        }
    }
    
    override suspend fun getTicketMessages(
        ticketId: String,
        limit: Int,
        offset: Int
    ): Response<TicketMessagesResponse> {
        simulateNetworkDelay()
        
        try {
            val messages = mockTicketMessages[ticketId]
                ?: return createErrorResponse(404, "Ticket not found")
            
            val sortedMessages = messages.sortedBy { it.created_at }
            val paginatedMessages = sortedMessages.drop(offset).take(limit)
            
            val response = TicketMessagesResponse(
                messages = paginatedMessages,
                total_count = messages.size,
                limit = limit,
                offset = offset
            )
            
            return Response.success(response)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Failed to get ticket messages: ${e.message}")
        }
    }
    
    override suspend fun addTicketMessage(
        ticketId: String,
        request: AddTicketMessageRequest
    ): Response<TicketMessageResponse> {
        simulateNetworkDelay()
        
        try {
            val messages = mockTicketMessages[ticketId]
                ?: return createErrorResponse(404, "Ticket not found")
            
            val messageId = UUID.randomUUID().toString()
            val now = System.currentTimeMillis()
            
            val message = TicketMessage(
                message_id = messageId,
                ticket_id = ticketId,
                sender_type = "user",
                sender_id = "current_user",
                content = request.content,
                attachments = request.attachment_ids?.map { attachmentId ->
                    TicketAttachment(
                        attachment_id = attachmentId,
                        name = "attachment_${Random.nextInt(1000, 9999)}.pdf",
                        url = "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf",
                        size = Random.nextLong(50000, 5000000),
                        mime_type = "application/pdf"
                    )
                },
                created_at = now
            )
            
            messages.add(message)
            
            // Update ticket status
            mockTickets[ticketId] = mockTickets[ticketId]!!.copy(
                status = "in_progress",
                updated_at = now
            )
            
            // Simulate support response
            simulateSupportResponse(ticketId)
            
            val response = TicketMessageResponse(message = message)
            return Response.success(response)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Failed to add message: ${e.message}")
        }
    }
    
    // Helper methods
    private fun generateMockTickets() {
        val categories = listOf("Technical Issue", "Account Problem", "Feature Request", "Bug Report", "General Inquiry")
        val statuses = listOf("open", "in_progress", "resolved", "closed")
        val priorities = listOf("low", "medium", "high", "urgent")
        
        repeat(5) { index ->
            val ticketId = UUID.randomUUID().toString()
            val now = System.currentTimeMillis() - Random.nextLong(0, 30 * 24 * 60 * 60 * 1000) // Last 30 days
            
            val ticket = Ticket(
                ticket_id = ticketId,
                subject = "Mock Support Ticket #${index + 1}",
                status = statuses.random(),
                priority = priorities.random(),
                category = categories.random(),
                created_at = now,
                updated_at = now + Random.nextLong(0, 24 * 60 * 60 * 1000),
                resolved_at = if (Random.nextBoolean()) now + Random.nextLong(0, 7 * 24 * 60 * 60 * 1000) else null
            )
            
            mockTickets[ticketId] = ticket
            mockTicketMessages[ticketId] = mutableListOf()
        }
    }
    
    private suspend fun simulateAutoResponse(ticketId: String) {
        delay(2000) // 2 second delay for auto-response
        
        val autoResponse = TicketMessage(
            message_id = UUID.randomUUID().toString(),
            ticket_id = ticketId,
            sender_type = "support",
            sender_id = "support_bot",
            content = "Thank you for contacting support! We've received your ticket and will respond within 24 hours. Ticket ID: $ticketId",
            attachments = null,
            created_at = System.currentTimeMillis()
        )
        
        mockTicketMessages[ticketId]?.add(autoResponse)
    }
    
    private suspend fun simulateSupportResponse(ticketId: String) {
        delay(5000) // 5 second delay for support response
        
        val supportResponses = listOf(
            "Thank you for the additional information. We're looking into this issue.",
            "We've escalated your ticket to our technical team for further investigation.",
            "Could you please provide more details about when this issue started?",
            "We've identified the issue and are working on a fix. We'll update you soon.",
            "This issue has been resolved. Please let us know if you continue to experience problems."
        )
        
        val supportResponse = TicketMessage(
            message_id = UUID.randomUUID().toString(),
            ticket_id = ticketId,
            sender_type = "support",
            sender_id = "support_agent_${Random.nextInt(1, 10)}",
            content = supportResponses.random(),
            attachments = null,
            created_at = System.currentTimeMillis()
        )
        
        mockTicketMessages[ticketId]?.add(supportResponse)
    }
    
    private suspend fun simulateNetworkDelay() {
        if (AppConfig.MockConfig.NETWORK_DELAY_MIN > 0) {
            val delay = Random.nextLong(
                AppConfig.MockConfig.NETWORK_DELAY_MIN,
                AppConfig.MockConfig.NETWORK_DELAY_MAX
            )
            delay(delay)
        }
    }
    
    private fun <T> createErrorResponse(code: Int, message: String): Response<T> {
        return Response.error(
            code,
            okhttp3.ResponseBody.create(
                okhttp3.MediaType.parse("application/json"),
                """{"error": "$message", "status": $code}"""
            )
        )
    }
}
