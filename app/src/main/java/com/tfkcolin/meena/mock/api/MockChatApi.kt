package com.tfkcolin.meena.mock.api

import com.tfkcolin.meena.config.AppConfig
import com.tfkcolin.meena.data.api.ChatApi
import com.tfkcolin.meena.data.models.*
import com.tfkcolin.meena.mock.ai.AIResponseGenerator
import com.tfkcolin.meena.mock.data.MockDataGenerator
import com.tfkcolin.meena.mock.storage.MockDataStorage
import kotlinx.coroutines.delay
import retrofit2.Response
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.random.Random

/**
 * Mock implementation of Chat<PERSON><PERSON> with AI-powered responses.
 */
@Singleton
class MockChatApi @Inject constructor(
    private val mockDataStorage: MockDataStorage,
    private val mockAuthApi: MockAuthApi,
    private val aiResponseGenerator: AIResponseGenerator = AIResponseGenerator()
) : Chat<PERSON><PERSON> {
    
    override suspend fun getChats(
        authToken: String,
        limit: Int,
        offset: Int
    ): Response<ChatListResponse> {
        simulateNetworkDelay()
        
        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")
            
            // Get all chats where user is a participant
            val allChats = mockDataStorage.getChats().values
            val userChats = allChats.filter { chat ->
                chat.getParticipantIdsList().contains(userId)
            }.sortedByDescending { it.lastMessageTimestamp ?: 0 }
            
            // Apply pagination
            val paginatedChats = userChats.drop(offset).take(limit)
            
            // Convert to response format
            val chatResponses = paginatedChats.map { chat ->
                ChatResponse(
                    id = chat.id,
                    conversationType = chat.conversationType,
                    privacyType = chat.privacyType,
                    participantIds = chat.participantIds,
                    name = chat.name,
                    description = chat.description,
                    avatarUrl = chat.avatarUrl,
                    adminIds = chat.adminIds,
                    createdBy = chat.createdBy,
                    lastMessage = chat.lastMessage,
                    lastMessageTimestamp = chat.lastMessageTimestamp,
                    unreadCount = chat.unreadCount,
                    isArchived = chat.isArchived,
                    isMuted = chat.isMuted,
                    isPinned = chat.isPinned,
                    mutedUntil = chat.mutedUntil,
                    createdAt = chat.createdAt,
                    isEncrypted = chat.isEncrypted,
                    lastMessageSenderId = chat.lastMessageSenderId,
                    lastMessageType = chat.lastMessageType
                )
            }
            
            val response = ChatListResponse(
                chats = chatResponses,
                totalCount = userChats.size,
                hasMore = offset + limit < userChats.size
            )
            
            return Response.success(response)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }
    
    override suspend fun getMessages(
        authToken: String,
        chatId: String,
        limit: Int,
        beforeMessageId: String?
    ): Response<MessageListResponse> {
        simulateNetworkDelay()
        
        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")
            
            // Verify user has access to this chat
            val chat = mockDataStorage.getChat(chatId)
                ?: return createErrorResponse(404, "Chat not found")
            
            if (!chat.getParticipantIdsList().contains(userId)) {
                return createErrorResponse(403, "Access denied")
            }
            
            // Get messages for this chat
            val allMessages = mockDataStorage.getMessages(chatId)
                .sortedByDescending { it.timestamp }
            
            // Apply cursor pagination if beforeMessageId is provided
            val filteredMessages = if (beforeMessageId != null) {
                val beforeIndex = allMessages.indexOfFirst { it.id == beforeMessageId }
                if (beforeIndex != -1) {
                    allMessages.drop(beforeIndex + 1)
                } else {
                    allMessages
                }
            } else {
                allMessages
            }
            
            // Apply limit
            val paginatedMessages = filteredMessages.take(limit)
            
            // Convert to response format
            val messageResponses = paginatedMessages.map { message ->
                MessageResponse(
                    id = message.id,
                    chatId = message.chatId,
                    senderId = message.senderId,
                    content = message.content,
                    messageType = message.messageType,
                    timestamp = message.timestamp,
                    isEdited = message.isEdited,
                    editedAt = message.editedAt,
                    replyToMessageId = message.replyToMessageId,
                    isDeleted = message.isDeleted,
                    deletedAt = message.deletedAt,
                    deliveryStatus = message.deliveryStatus,
                    readBy = message.readBy,
                    reactions = message.reactions,
                    mediaAttachments = message.mediaAttachments,
                    isEncrypted = message.isEncrypted,
                    encryptionKeyId = message.encryptionKeyId
                )
            }
            
            val response = MessageListResponse(
                messages = messageResponses,
                hasMore = filteredMessages.size > limit
            )
            
            return Response.success(response)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }
    
    override suspend fun sendMessage(
        authToken: String,
        chatId: String,
        request: SendMessageRequest
    ): Response<MessageResponse> {
        simulateNetworkDelay()
        
        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")
            
            // Verify user has access to this chat
            val chat = mockDataStorage.getChat(chatId)
                ?: return createErrorResponse(404, "Chat not found")
            
            if (!chat.getParticipantIdsList().contains(userId)) {
                return createErrorResponse(403, "Access denied")
            }
            
            // Create new message
            val message = Message(
                id = UUID.randomUUID().toString(),
                chatId = chatId,
                senderId = userId,
                content = request.content,
                messageType = request.messageType ?: "text",
                timestamp = System.currentTimeMillis(),
                isEdited = false,
                editedAt = null,
                replyToMessageId = request.replyToMessageId,
                isDeleted = false,
                deletedAt = null,
                deliveryStatus = "sent",
                readBy = listOf(userId),
                reactions = emptyMap(),
                mediaAttachments = request.mediaAttachments ?: emptyList(),
                isEncrypted = chat.isEncrypted,
                encryptionKeyId = if (chat.isEncrypted) UUID.randomUUID().toString() else null
            )
            
            // Save message
            mockDataStorage.addMessage(message)
            
            // Update chat's last message
            val updatedChat = chat.copy(
                lastMessage = message.content ?: "Media",
                lastMessageTimestamp = message.timestamp,
                lastMessageSenderId = userId,
                lastMessageType = message.messageType
            )
            mockDataStorage.updateChat(updatedChat)
            
            // Generate AI response if this is a one-to-one chat and the message is from current user
            if (chat.isOneToOne() && AppConfig.FeatureFlags.ENABLE_AI_CHAT_RESPONSES) {
                generateAIResponse(chat, message, userId)
            }
            
            val messageResponse = MessageResponse(
                id = message.id,
                chatId = message.chatId,
                senderId = message.senderId,
                content = message.content,
                messageType = message.messageType,
                timestamp = message.timestamp,
                isEdited = message.isEdited,
                editedAt = message.editedAt,
                replyToMessageId = message.replyToMessageId,
                isDeleted = message.isDeleted,
                deletedAt = message.deletedAt,
                deliveryStatus = message.deliveryStatus,
                readBy = message.readBy,
                reactions = message.reactions,
                mediaAttachments = message.mediaAttachments,
                isEncrypted = message.isEncrypted,
                encryptionKeyId = message.encryptionKeyId
            )
            
            return Response.success(messageResponse)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }
    
    override suspend fun editMessage(
        authToken: String,
        messageId: String,
        request: EditMessageRequest
    ): Response<MessageResponse> {
        simulateNetworkDelay()
        
        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")
            
            // Find the message
            val allChats = mockDataStorage.getChats()
            var targetMessage: Message? = null
            var targetChatId: String? = null
            
            for ((chatId, _) in allChats) {
                val messages = mockDataStorage.getMessages(chatId)
                val message = messages.find { it.id == messageId }
                if (message != null) {
                    targetMessage = message
                    targetChatId = chatId
                    break
                }
            }
            
            if (targetMessage == null || targetChatId == null) {
                return createErrorResponse(404, "Message not found")
            }
            
            // Verify user owns the message
            if (targetMessage.senderId != userId) {
                return createErrorResponse(403, "Access denied")
            }
            
            // Update message
            val updatedMessage = targetMessage.copy(
                content = request.content,
                isEdited = true,
                editedAt = System.currentTimeMillis()
            )
            
            mockDataStorage.updateMessage(updatedMessage)
            
            val messageResponse = MessageResponse(
                id = updatedMessage.id,
                chatId = updatedMessage.chatId,
                senderId = updatedMessage.senderId,
                content = updatedMessage.content,
                messageType = updatedMessage.messageType,
                timestamp = updatedMessage.timestamp,
                isEdited = updatedMessage.isEdited,
                editedAt = updatedMessage.editedAt,
                replyToMessageId = updatedMessage.replyToMessageId,
                isDeleted = updatedMessage.isDeleted,
                deletedAt = updatedMessage.deletedAt,
                deliveryStatus = updatedMessage.deliveryStatus,
                readBy = updatedMessage.readBy,
                reactions = updatedMessage.reactions,
                mediaAttachments = updatedMessage.mediaAttachments,
                isEncrypted = updatedMessage.isEncrypted,
                encryptionKeyId = updatedMessage.encryptionKeyId
            )
            
            return Response.success(messageResponse)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }
    
    override suspend fun deleteMessage(
        authToken: String,
        messageId: String
    ): Response<Unit> {
        simulateNetworkDelay()
        
        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")
            
            // Find and delete the message
            val allChats = mockDataStorage.getChats()
            var found = false
            
            for ((chatId, _) in allChats) {
                val messages = mockDataStorage.getMessages(chatId)
                val message = messages.find { it.id == messageId }
                if (message != null && message.senderId == userId) {
                    mockDataStorage.deleteMessage(chatId, messageId)
                    found = true
                    break
                }
            }
            
            if (!found) {
                return createErrorResponse(404, "Message not found or access denied")
            }
            
            return Response.success(Unit)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }
    
    override suspend fun markAsRead(
        authToken: String,
        chatId: String,
        request: MarkAsReadRequest
    ): Response<Unit> {
        simulateNetworkDelay()
        
        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")
            
            // Verify user has access to this chat
            val chat = mockDataStorage.getChat(chatId)
                ?: return createErrorResponse(404, "Chat not found")
            
            if (!chat.getParticipantIdsList().contains(userId)) {
                return createErrorResponse(403, "Access denied")
            }
            
            // Update chat's unread count for this user (simplified)
            val updatedChat = chat.copy(unreadCount = 0)
            mockDataStorage.updateChat(updatedChat)
            
            return Response.success(Unit)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }
    
    // Helper methods
    private suspend fun simulateNetworkDelay() {
        if (AppConfig.MockConfig.NETWORK_DELAY_MIN > 0) {
            val delay = Random.nextLong(
                AppConfig.MockConfig.NETWORK_DELAY_MIN,
                AppConfig.MockConfig.NETWORK_DELAY_MAX
            )
            delay(delay)
        }
    }
    
    private fun extractUserIdFromToken(authToken: String): String? {
        val token = authToken.removePrefix("Bearer ").trim()
        return mockAuthApi.validateToken(token)
    }
    
    private fun <T> createErrorResponse(code: Int, message: String): Response<T> {
        return Response.error(
            code,
            okhttp3.ResponseBody.create(
                okhttp3.MediaType.parse("application/json"),
                """{"error": "$message", "status": $code}"""
            )
        )
    }
    
    /**
     * Generate AI response for one-to-one chats.
     */
    private suspend fun generateAIResponse(chat: Chat, userMessage: Message, currentUserId: String) {
        try {
            // Get the other participant (not the current user)
            val otherParticipantId = chat.getParticipantIdsList().find { it != currentUserId }
                ?: return
            
            // Check if we should generate a response (not always)
            if (Random.nextFloat() > 0.8f) return // 80% chance of responding
            
            // Generate AI response
            val aiResponse = aiResponseGenerator.generateResponse(
                userMessage = userMessage.content ?: "",
                chatId = chat.id,
                personaId = null, // Random persona
                isFirstMessage = mockDataStorage.getMessages(chat.id).size <= 1
            )
            
            // Create AI message
            val aiMessage = Message(
                id = UUID.randomUUID().toString(),
                chatId = chat.id,
                senderId = otherParticipantId,
                content = aiResponse.text,
                messageType = "text",
                timestamp = System.currentTimeMillis() + Random.nextLong(1000, 5000), // Slight delay
                isEdited = false,
                editedAt = null,
                replyToMessageId = null,
                isDeleted = false,
                deletedAt = null,
                deliveryStatus = "sent",
                readBy = listOf(otherParticipantId),
                reactions = emptyMap(),
                mediaAttachments = emptyList(),
                isEncrypted = chat.isEncrypted,
                encryptionKeyId = if (chat.isEncrypted) UUID.randomUUID().toString() else null
            )
            
            // Add delay before sending AI response
            delay(aiResponse.typingDuration)
            
            // Save AI message
            mockDataStorage.addMessage(aiMessage)
            
            // Update chat's last message
            val updatedChat = chat.copy(
                lastMessage = aiMessage.content,
                lastMessageTimestamp = aiMessage.timestamp,
                lastMessageSenderId = otherParticipantId,
                lastMessageType = aiMessage.messageType,
                unreadCount = chat.unreadCount + 1 // Increment unread count
            )
            mockDataStorage.updateChat(updatedChat)
            
        } catch (e: Exception) {
            // Log error but don't fail the original message send
            if (AppConfig.DevConfig.ENABLE_MOCK_LOGGING) {
                println("Error generating AI response: ${e.message}")
            }
        }
    }
}
