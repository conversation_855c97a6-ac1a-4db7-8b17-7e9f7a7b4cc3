package com.tfkcolin.meena.mock.storage

import android.content.Context
import android.content.SharedPreferences
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.tfkcolin.meena.config.AppConfig
import com.tfkcolin.meena.data.models.*
import com.tfkcolin.meena.mock.data.MockDataGenerator
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Persistent storage for mock data that survives app restarts.
 */
@Singleton
class MockDataStorage @Inject constructor(
    @ApplicationContext private val context: Context,
    private val gson: Gson
) {
    
    private val prefs: SharedPreferences = context.getSharedPreferences(
        "mock_data_storage", 
        Context.MODE_PRIVATE
    )
    
    // In-memory caches for performance
    private var _users: MutableMap<String, User>? = null
    private var _contacts: MutableMap<String, MutableList<Contact>>? = null
    private var _chats: MutableMap<String, Chat>? = null
    private var _messages: MutableMap<String, MutableList<Message>>? = null
    private var _currentUser: User? = null
    
    // Keys for SharedPreferences
    private companion object {
        const val KEY_USERS = "mock_users"
        const val KEY_CONTACTS = "mock_contacts"
        const val KEY_CHATS = "mock_chats"
        const val KEY_MESSAGES = "mock_messages"
        const val KEY_CURRENT_USER = "mock_current_user"
        const val KEY_INITIALIZED = "mock_data_initialized"
    }
    
    /**
     * Initialize mock data if not already done.
     */
    suspend fun initializeIfNeeded() = withContext(Dispatchers.IO) {
        if (!isInitialized() || AppConfig.DevConfig.RESET_MOCK_DATA_ON_START) {
            generateInitialData()
            markAsInitialized()
        }
        loadDataIntoMemory()
    }
    
    /**
     * Check if mock data has been initialized.
     */
    private fun isInitialized(): Boolean {
        return prefs.getBoolean(KEY_INITIALIZED, false)
    }
    
    /**
     * Mark mock data as initialized.
     */
    private fun markAsInitialized() {
        prefs.edit().putBoolean(KEY_INITIALIZED, true).apply()
    }
    
    /**
     * Generate initial mock data.
     */
    private suspend fun generateInitialData() = withContext(Dispatchers.IO) {
        // Generate users
        val users = mutableMapOf<String, User>()
        repeat(AppConfig.MockConfig.DEFAULT_MOCK_USERS_COUNT) {
            val user = MockDataGenerator.generateMockUser()
            users[user.userId] = user
        }
        
        // Set current user (first generated user)
        val currentUser = users.values.first()
        
        // Generate contacts for current user
        val contacts = mutableMapOf<String, MutableList<Contact>>()
        val currentUserContacts = mutableListOf<Contact>()
        
        // Add some of the other users as contacts
        users.values.filter { it.userId != currentUser.userId }.take(15).forEach { user ->
            val contact = MockDataGenerator.generateMockContact(currentUser.userId, user)
            currentUserContacts.add(contact)
        }
        contacts[currentUser.userId] = currentUserContacts
        
        // Generate chats
        val chats = mutableMapOf<String, Chat>()
        val messages = mutableMapOf<String, MutableList<Message>>()
        
        // Generate one-to-one chats
        currentUserContacts.take(8).forEach { contact ->
            val chat = MockDataGenerator.generateMockChat(
                participants = listOf(currentUser.userId, contact.contactId),
                type = ConversationType.ONE_TO_ONE
            )
            chats[chat.id] = chat

            // Generate messages for this chat
            val chatMessages = mutableListOf<Message>()
            repeat(kotlin.random.Random.nextInt(5, 25)) {
                val senderId = listOf(currentUser.userId, contact.contactId).random()
                val recipientId = if (senderId == currentUser.userId) contact.contactId else currentUser.userId
                val message = MockDataGenerator.generateMockMessage(chat.id, senderId, recipientId)
                chatMessages.add(message)
            }
            chatMessages.sortBy { it.timestamp }
            messages[chat.id] = chatMessages
        }
        
        // Generate group chats
        repeat(3) {
            val groupParticipants = mutableListOf(currentUser.userId)
            groupParticipants.addAll(
                currentUserContacts.shuffled().take(kotlin.random.Random.nextInt(3, 8))
                    .map { it.contactId }
            )
            
            val groupChat = MockDataGenerator.generateMockChat(
                participants = groupParticipants,
                type = ConversationType.GROUP
            )
            chats[groupChat.id] = groupChat
            
            // Generate messages for group chat
            val groupMessages = mutableListOf<Message>()
            repeat(kotlin.random.Random.nextInt(10, 40)) {
                val senderId = groupParticipants.random()
                val message = MockDataGenerator.generateMockMessage(groupChat.id, senderId, "")
                groupMessages.add(message)
            }
            groupMessages.sortBy { it.timestamp }
            messages[groupChat.id] = groupMessages
        }
        
        // Generate channels
        repeat(2) {
            val channelParticipants = mutableListOf(currentUser.userId)
            channelParticipants.addAll(
                currentUserContacts.shuffled().take(kotlin.random.Random.nextInt(5, 15))
                    .map { it.contactId }
            )
            
            val channel = MockDataGenerator.generateMockChat(
                participants = channelParticipants,
                type = ConversationType.CHANNEL
            )
            chats[channel.id] = channel
            
            // Generate messages for channel
            val channelMessages = mutableListOf<Message>()
            repeat(kotlin.random.Random.nextInt(15, 50)) {
                val senderId = channelParticipants.random()
                val message = MockDataGenerator.generateMockMessage(channel.id, senderId, "")
                channelMessages.add(message)
            }
            channelMessages.sortBy { it.timestamp }
            messages[channel.id] = channelMessages
        }
        
        // Save to persistent storage
        saveUsers(users)
        saveContacts(contacts)
        saveChats(chats)
        saveMessages(messages)
        saveCurrentUser(currentUser)
    }
    
    /**
     * Load data from persistent storage into memory.
     */
    private fun loadDataIntoMemory() {
        _users = loadUsers().toMutableMap()
        _contacts = loadContacts().toMutableMap()
        _chats = loadChats().toMutableMap()
        _messages = loadMessages().toMutableMap()
        _currentUser = loadCurrentUser()
    }
    
    // Users
    fun getUsers(): Map<String, User> = _users ?: emptyMap()
    fun getUser(id: String): User? = _users?.get(id)
    fun getCurrentUser(): User? = _currentUser
    
    fun addUser(user: User) {
        _users = (_users ?: mutableMapOf()).apply { put(user.userId, user) }
        saveUsers(_users!!)
    }

    fun updateUser(user: User) {
        _users?.put(user.userId, user)
        _users?.let { saveUsers(it) }
        if (_currentUser?.userId == user.userId) {
            _currentUser = user
            saveCurrentUser(user)
        }
    }
    
    // Contacts
    fun getContacts(userId: String): List<Contact> = _contacts?.get(userId) ?: emptyList()
    
    fun addContact(userId: String, contact: Contact) {
        val userContacts = _contacts?.getOrPut(userId) { mutableListOf() } ?: mutableListOf()
        userContacts.add(contact)
        _contacts?.let { saveContacts(it) }
    }
    
    fun removeContact(userId: String, contactId: String) {
        _contacts?.get(userId)?.removeAll { it.id == contactId }
        _contacts?.let { saveContacts(it) }
    }
    
    // Chats
    fun getChats(): Map<String, Chat> = _chats ?: emptyMap()
    fun getChat(id: String): Chat? = _chats?.get(id)
    
    fun addChat(chat: Chat) {
        _chats = (_chats ?: mutableMapOf()).apply { put(chat.id, chat) }
        saveChats(_chats!!)
    }
    
    fun updateChat(chat: Chat) {
        _chats?.put(chat.id, chat)
        _chats?.let { saveChats(it) }
    }
    
    // Messages
    fun getMessages(chatId: String): List<Message> = _messages?.get(chatId) ?: emptyList()
    
    fun addMessage(message: Message) {
        val chatMessages = _messages?.getOrPut(message.chatId) { mutableListOf() } ?: mutableListOf()
        chatMessages.add(message)
        chatMessages.sortBy { it.timestamp }
        _messages?.let { saveMessages(it) }
    }
    
    fun updateMessage(message: Message) {
        _messages?.get(message.chatId)?.let { messages ->
            val index = messages.indexOfFirst { it.id == message.id }
            if (index != -1) {
                messages[index] = message
                _messages?.let { saveMessages(it) }
            }
        }
    }
    
    fun deleteMessage(chatId: String, messageId: String) {
        _messages?.get(chatId)?.removeAll { it.id == messageId }
        _messages?.let { saveMessages(it) }
    }
    
    // Persistence methods
    private fun saveUsers(users: Map<String, User>) {
        val json = gson.toJson(users)
        prefs.edit().putString(KEY_USERS, json).apply()
    }
    
    private fun loadUsers(): Map<String, User> {
        val json = prefs.getString(KEY_USERS, null) ?: return emptyMap()
        val type = object : TypeToken<Map<String, User>>() {}.type
        return gson.fromJson(json, type) ?: emptyMap()
    }
    
    private fun saveContacts(contacts: Map<String, MutableList<Contact>>) {
        val json = gson.toJson(contacts)
        prefs.edit().putString(KEY_CONTACTS, json).apply()
    }
    
    private fun loadContacts(): Map<String, MutableList<Contact>> {
        val json = prefs.getString(KEY_CONTACTS, null) ?: return emptyMap()
        val type = object : TypeToken<Map<String, MutableList<Contact>>>() {}.type
        return gson.fromJson(json, type) ?: emptyMap()
    }
    
    private fun saveChats(chats: Map<String, Chat>) {
        val json = gson.toJson(chats)
        prefs.edit().putString(KEY_CHATS, json).apply()
    }
    
    private fun loadChats(): Map<String, Chat> {
        val json = prefs.getString(KEY_CHATS, null) ?: return emptyMap()
        val type = object : TypeToken<Map<String, Chat>>() {}.type
        return gson.fromJson(json, type) ?: emptyMap()
    }
    
    private fun saveMessages(messages: Map<String, MutableList<Message>>) {
        val json = gson.toJson(messages)
        prefs.edit().putString(KEY_MESSAGES, json).apply()
    }
    
    private fun loadMessages(): Map<String, MutableList<Message>> {
        val json = prefs.getString(KEY_MESSAGES, null) ?: return emptyMap()
        val type = object : TypeToken<Map<String, MutableList<Message>>>() {}.type
        return gson.fromJson(json, type) ?: emptyMap()
    }
    
    private fun saveCurrentUser(user: User) {
        val json = gson.toJson(user)
        prefs.edit().putString(KEY_CURRENT_USER, json).apply()
    }
    
    private fun loadCurrentUser(): User? {
        val json = prefs.getString(KEY_CURRENT_USER, null) ?: return null
        return gson.fromJson(json, User::class.java)
    }
    
    /**
     * Clear all mock data.
     */
    fun clearAllData() {
        prefs.edit().clear().apply()
        _users = null
        _contacts = null
        _chats = null
        _messages = null
        _currentUser = null
    }
}
