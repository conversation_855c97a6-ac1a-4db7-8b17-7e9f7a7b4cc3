package com.tfkcolin.meena.mock.api

import com.tfkcolin.meena.config.AppConfig
import com.tfkcolin.meena.data.api.MediaApi
import com.tfkcolin.meena.data.models.MediaAttachment
import com.tfkcolin.meena.data.models.MediaUploadResponse
import kotlinx.coroutines.delay
import okhttp3.MultipartBody
import okhttp3.RequestBody
import retrofit2.Response
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.random.Random

/**
 * Mock implementation of MediaApi for testing and development.
 */
@Singleton
class MockMediaApi @Inject constructor(
    private val mockAuthApi: MockAuthApi
) : MediaApi {
    
    override suspend fun uploadMedia(
        authToken: String,
        file: MultipartBody.Part,
        messageId: RequestBody?,
        description: RequestBody?
    ): Response<MediaUploadResponse> {
        simulateNetworkDelay()
        
        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")
            
            // Simulate file upload processing
            simulateUploadDelay()
            
            // Generate mock media attachment
            val mediaId = UUID.randomUUID().toString()
            val fileName = "mock_file_${Random.nextInt(1000, 9999)}.jpg"
            val fileSize = Random.nextLong(100000, 10000000) // 100KB to 10MB
            
            val mediaAttachment = MediaAttachment(
                id = mediaId,
                messageId = messageId?.toString() ?: "",
                fileName = fileName,
                fileSize = fileSize,
                mimeType = "image/jpeg",
                url = "https://picsum.photos/800/600?random=${Random.nextInt()}",
                thumbnailUrl = "https://picsum.photos/200/150?random=${Random.nextInt()}",
                duration = null, // For images
                uploadedAt = System.currentTimeMillis()
            )
            
            val response = MediaUploadResponse(
                mediaId = mediaId,
                fileName = fileName,
                fileSize = fileSize,
                mimeType = "image/jpeg",
                url = mediaAttachment.url,
                thumbnailUrl = mediaAttachment.thumbnailUrl,
                uploadedAt = mediaAttachment.uploadedAt
            )
            
            return Response.success(response)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Upload failed: ${e.message}")
        }
    }
    
    override suspend fun uploadVideo(
        authToken: String,
        file: MultipartBody.Part,
        messageId: RequestBody?,
        description: RequestBody?
    ): Response<MediaUploadResponse> {
        simulateNetworkDelay()
        
        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")
            
            // Simulate video upload processing (longer delay)
            simulateVideoUploadDelay()
            
            // Generate mock video attachment
            val mediaId = UUID.randomUUID().toString()
            val fileName = "mock_video_${Random.nextInt(1000, 9999)}.mp4"
            val fileSize = Random.nextLong(1000000, 50000000) // 1MB to 50MB
            val duration = Random.nextLong(10000, 300000) // 10 seconds to 5 minutes
            
            val response = MediaUploadResponse(
                mediaId = mediaId,
                fileName = fileName,
                fileSize = fileSize,
                mimeType = "video/mp4",
                url = "https://sample-videos.com/zip/10/mp4/SampleVideo_360x240_1mb.mp4",
                thumbnailUrl = "https://picsum.photos/360/240?random=${Random.nextInt()}",
                uploadedAt = System.currentTimeMillis(),
                duration = duration
            )
            
            return Response.success(response)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Video upload failed: ${e.message}")
        }
    }
    
    override suspend fun uploadAudio(
        authToken: String,
        file: MultipartBody.Part,
        messageId: RequestBody?,
        description: RequestBody?
    ): Response<MediaUploadResponse> {
        simulateNetworkDelay()
        
        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")
            
            // Simulate audio upload processing
            simulateUploadDelay()
            
            // Generate mock audio attachment
            val mediaId = UUID.randomUUID().toString()
            val fileName = "mock_audio_${Random.nextInt(1000, 9999)}.mp3"
            val fileSize = Random.nextLong(500000, 10000000) // 500KB to 10MB
            val duration = Random.nextLong(5000, 600000) // 5 seconds to 10 minutes
            
            val response = MediaUploadResponse(
                mediaId = mediaId,
                fileName = fileName,
                fileSize = fileSize,
                mimeType = "audio/mp3",
                url = "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",
                thumbnailUrl = null, // Audio doesn't have thumbnails
                uploadedAt = System.currentTimeMillis(),
                duration = duration
            )
            
            return Response.success(response)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Audio upload failed: ${e.message}")
        }
    }
    
    override suspend fun uploadFile(
        authToken: String,
        file: MultipartBody.Part,
        messageId: RequestBody?,
        description: RequestBody?
    ): Response<MediaUploadResponse> {
        simulateNetworkDelay()
        
        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")
            
            // Simulate file upload processing
            simulateUploadDelay()
            
            // Generate mock file attachment
            val mediaId = UUID.randomUUID().toString()
            val fileExtensions = listOf("pdf", "doc", "txt", "zip", "xlsx")
            val extension = fileExtensions.random()
            val fileName = "mock_document_${Random.nextInt(1000, 9999)}.$extension"
            val fileSize = Random.nextLong(50000, 5000000) // 50KB to 5MB
            
            val mimeType = when (extension) {
                "pdf" -> "application/pdf"
                "doc" -> "application/msword"
                "txt" -> "text/plain"
                "zip" -> "application/zip"
                "xlsx" -> "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                else -> "application/octet-stream"
            }
            
            val response = MediaUploadResponse(
                mediaId = mediaId,
                fileName = fileName,
                fileSize = fileSize,
                mimeType = mimeType,
                url = "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf",
                thumbnailUrl = null, // Files don't have thumbnails
                uploadedAt = System.currentTimeMillis()
            )
            
            return Response.success(response)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "File upload failed: ${e.message}")
        }
    }
    
    override suspend fun deleteMedia(
        authToken: String,
        mediaId: String
    ): Response<Unit> {
        simulateNetworkDelay()
        
        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")
            
            // In a real implementation, you'd delete the media file
            // For mock, we just simulate success
            
            return Response.success(Unit)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Delete failed: ${e.message}")
        }
    }
    
    // Helper methods
    private suspend fun simulateNetworkDelay() {
        if (AppConfig.MockConfig.NETWORK_DELAY_MIN > 0) {
            val delay = Random.nextLong(
                AppConfig.MockConfig.NETWORK_DELAY_MIN,
                AppConfig.MockConfig.NETWORK_DELAY_MAX
            )
            delay(delay)
        }
    }
    
    private suspend fun simulateUploadDelay() {
        // Simulate file upload time (1-3 seconds)
        delay(Random.nextLong(1000, 3000))
    }
    
    private suspend fun simulateVideoUploadDelay() {
        // Simulate video upload time (3-8 seconds)
        delay(Random.nextLong(3000, 8000))
    }
    
    private fun extractUserIdFromToken(authToken: String): String? {
        val token = authToken.removePrefix("Bearer ").trim()
        return mockAuthApi.validateToken(token)
    }
    
    override suspend fun uploadMedia(
        authToken: String,
        requestBody: RequestBody
    ): Response<MediaAttachment> {
        simulateNetworkDelay()

        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")

            simulateUploadDelay()

            val mediaId = UUID.randomUUID().toString()
            val mediaAttachment = MediaAttachment(
                id = mediaId,
                messageId = "",
                fileName = "mock_file_${Random.nextInt(1000, 9999)}.jpg",
                fileSize = Random.nextLong(100000, 10000000),
                mimeType = "image/jpeg",
                url = "https://picsum.photos/800/600?random=${Random.nextInt()}",
                thumbnailUrl = "https://picsum.photos/200/150?random=${Random.nextInt()}",
                duration = null,
                uploadedAt = System.currentTimeMillis()
            )

            return Response.success(mediaAttachment)

        } catch (e: Exception) {
            return createErrorResponse(500, "Upload failed: ${e.message}")
        }
    }

    private fun <T> createErrorResponse(code: Int, message: String): Response<T> {
        return Response.error(
            code,
            okhttp3.ResponseBody.create(
                okhttp3.MediaType.parse("application/json"),
                """{"error": "$message", "status": $code}"""
            )
        )
    }
}
