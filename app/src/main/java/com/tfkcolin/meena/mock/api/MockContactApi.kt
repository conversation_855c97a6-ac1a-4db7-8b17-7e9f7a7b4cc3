package com.tfkcolin.meena.mock.api

import com.tfkcolin.meena.config.AppConfig
import com.tfkcolin.meena.data.api.ContactApi
import com.tfkcolin.meena.data.models.*
import com.tfkcolin.meena.mock.data.MockDataGenerator
import com.tfkcolin.meena.mock.storage.MockDataStorage
import kotlinx.coroutines.delay
import retrofit2.Response
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.random.Random

/**
 * Mock implementation of ContactApi for testing and development.
 */
@Singleton
class MockContactApi @Inject constructor(
    private val mockDataStorage: MockDataStorage,
    private val mockAuthApi: MockAuthA<PERSON>
) : ContactApi {
    
    override suspend fun getContacts(
        authToken: String,
        limit: Int,
        offset: Int,
        relationship: String?
    ): Response<ContactListResponse> {
        simulateNetworkDelay()
        
        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")
            
            // Get contacts for the user
            val allContacts = mockDataStorage.getContacts(userId)
            
            // Filter by relationship if specified
            val filteredContacts = if (relationship != null) {
                allContacts.filter { it.relationship == relationship }
            } else {
                allContacts
            }
            
            // Sort by last interaction (most recent first)
            val sortedContacts = filteredContacts.sortedByDescending { it.lastInteraction }
            
            // Apply pagination
            val paginatedContacts = sortedContacts.drop(offset).take(limit)
            
            // Convert to response format
            val contactResponses = paginatedContacts.map { contact ->
                val contactUser = mockDataStorage.getUser(contact.contactUserId)
                ContactResponse(
                    id = contact.id,
                    userId = contact.userId,
                    contactUserId = contact.contactUserId,
                    contactUserHandle = contact.contactUserHandle,
                    displayName = contact.customDisplayName ?: contact.displayName,
                    avatarUrl = contact.avatarUrl,
                    relationship = contact.relationship,
                    isBlocked = contact.isBlocked,
                    isFavorite = contact.isFavorite,
                    addedAt = contact.addedAt,
                    lastInteraction = contact.lastInteraction,
                    isOnline = Random.nextBoolean(), // Mock online status
                    lastSeen = if (Random.nextBoolean()) System.currentTimeMillis() - Random.nextLong(0, 86400000) else null,
                    mutualContactsCount = Random.nextInt(0, 10),
                    isVerified = contactUser?.isVerified ?: false,
                    bio = contactUser?.bio
                )
            }
            
            val response = ContactListResponse(
                contacts = contactResponses,
                totalCount = filteredContacts.size,
                hasMore = offset + limit < filteredContacts.size
            )
            
            return Response.success(response)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }
    
    override suspend fun addContact(
        authToken: String,
        request: AddContactRequest
    ): Response<ContactResponse> {
        simulateNetworkDelay()
        
        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")
            
            // Find the user to add as contact
            val users = mockDataStorage.getUsers()
            val contactUser = users.values.find { 
                it.userHandle == request.contactUserHandle ||
                it.email == request.contactUserHandle ||
                it.phoneNumber == request.contactUserHandle
            } ?: return createErrorResponse(404, "User not found")
            
            // Check if contact already exists
            val existingContacts = mockDataStorage.getContacts(userId)
            if (existingContacts.any { it.contactUserId == contactUser.id }) {
                return createErrorResponse(409, "Contact already exists")
            }
            
            // Create new contact
            val contact = Contact(
                id = UUID.randomUUID().toString(),
                userId = userId,
                contactUserId = contactUser.id,
                contactUserHandle = contactUser.userHandle,
                displayName = contactUser.displayName,
                customDisplayName = request.customDisplayName,
                avatarUrl = contactUser.avatarUrl,
                relationship = "contact",
                isBlocked = false,
                isFavorite = false,
                addedAt = System.currentTimeMillis(),
                lastInteraction = System.currentTimeMillis()
            )
            
            // Save contact
            mockDataStorage.addContact(userId, contact)
            
            // Create response
            val contactResponse = ContactResponse(
                id = contact.id,
                userId = contact.userId,
                contactUserId = contact.contactUserId,
                contactUserHandle = contact.contactUserHandle,
                displayName = contact.customDisplayName ?: contact.displayName,
                avatarUrl = contact.avatarUrl,
                relationship = contact.relationship,
                isBlocked = contact.isBlocked,
                isFavorite = contact.isFavorite,
                addedAt = contact.addedAt,
                lastInteraction = contact.lastInteraction,
                isOnline = Random.nextBoolean(),
                lastSeen = System.currentTimeMillis() - Random.nextLong(0, 3600000),
                mutualContactsCount = Random.nextInt(0, 5),
                isVerified = contactUser.isVerified,
                bio = contactUser.bio
            )
            
            return Response.success(contactResponse)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }
    
    override suspend fun updateContact(
        authToken: String,
        contactId: String,
        request: UpdateContactRequest
    ): Response<ContactResponse> {
        simulateNetworkDelay()
        
        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")
            
            // Find the contact
            val contacts = mockDataStorage.getContacts(userId)
            val contact = contacts.find { it.id == contactId }
                ?: return createErrorResponse(404, "Contact not found")
            
            // Update contact
            val updatedContact = contact.copy(
                customDisplayName = request.customDisplayName,
                isFavorite = request.isFavorite ?: contact.isFavorite
            )
            
            // Remove old contact and add updated one
            mockDataStorage.removeContact(userId, contactId)
            mockDataStorage.addContact(userId, updatedContact)
            
            // Get contact user info
            val contactUser = mockDataStorage.getUser(contact.contactUserId)
            
            val contactResponse = ContactResponse(
                id = updatedContact.id,
                userId = updatedContact.userId,
                contactUserId = updatedContact.contactUserId,
                contactUserHandle = updatedContact.contactUserHandle,
                displayName = updatedContact.customDisplayName ?: updatedContact.displayName,
                avatarUrl = updatedContact.avatarUrl,
                relationship = updatedContact.relationship,
                isBlocked = updatedContact.isBlocked,
                isFavorite = updatedContact.isFavorite,
                addedAt = updatedContact.addedAt,
                lastInteraction = updatedContact.lastInteraction,
                isOnline = Random.nextBoolean(),
                lastSeen = System.currentTimeMillis() - Random.nextLong(0, 3600000),
                mutualContactsCount = Random.nextInt(0, 5),
                isVerified = contactUser?.isVerified ?: false,
                bio = contactUser?.bio
            )
            
            return Response.success(contactResponse)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }
    
    override suspend fun deleteContact(
        authToken: String,
        contactId: String
    ): Response<Unit> {
        simulateNetworkDelay()
        
        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")
            
            // Check if contact exists
            val contacts = mockDataStorage.getContacts(userId)
            val contact = contacts.find { it.id == contactId }
                ?: return createErrorResponse(404, "Contact not found")
            
            // Remove contact
            mockDataStorage.removeContact(userId, contactId)
            
            return Response.success(Unit)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }
    
    override suspend fun blockContact(
        authToken: String,
        request: BlockContactRequest
    ): Response<Unit> {
        simulateNetworkDelay()
        
        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")
            
            // Find the contact
            val contacts = mockDataStorage.getContacts(userId)
            val contact = contacts.find { it.contactUserHandle == request.contactUserHandle }
                ?: return createErrorResponse(404, "Contact not found")
            
            // Update contact to blocked
            val blockedContact = contact.copy(
                isBlocked = true,
                relationship = "blocked"
            )
            
            // Remove old contact and add updated one
            mockDataStorage.removeContact(userId, contact.id)
            mockDataStorage.addContact(userId, blockedContact)
            
            return Response.success(Unit)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }
    
    override suspend fun unblockContact(
        authToken: String,
        contactUserHandle: String
    ): Response<Unit> {
        simulateNetworkDelay()
        
        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")
            
            // Find the blocked contact
            val contacts = mockDataStorage.getContacts(userId)
            val contact = contacts.find { 
                it.contactUserHandle == contactUserHandle && it.isBlocked 
            } ?: return createErrorResponse(404, "Blocked contact not found")
            
            // Update contact to unblocked
            val unblockedContact = contact.copy(
                isBlocked = false,
                relationship = "contact"
            )
            
            // Remove old contact and add updated one
            mockDataStorage.removeContact(userId, contact.id)
            mockDataStorage.addContact(userId, unblockedContact)
            
            return Response.success(Unit)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }
    
    override suspend fun searchContacts(
        authToken: String,
        query: String,
        limit: Int
    ): Response<ContactSearchResponse> {
        simulateNetworkDelay()
        
        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")
            
            // Search in all users (excluding current user)
            val allUsers = mockDataStorage.getUsers().values
            val searchResults = allUsers.filter { user ->
                user.id != userId && (
                    user.userHandle.contains(query, ignoreCase = true) ||
                    user.displayName.contains(query, ignoreCase = true) ||
                    user.email?.contains(query, ignoreCase = true) == true
                )
            }.take(limit)
            
            // Convert to search result format
            val searchResultResponses = searchResults.map { user ->
                ContactSearchResult(
                    userId = user.id,
                    userHandle = user.userHandle,
                    displayName = user.displayName,
                    avatarUrl = user.avatarUrl,
                    bio = user.bio,
                    isVerified = user.isVerified,
                    mutualContactsCount = Random.nextInt(0, 5),
                    isContact = mockDataStorage.getContacts(userId).any { it.contactUserId == user.id }
                )
            }
            
            val response = ContactSearchResponse(
                results = searchResultResponses,
                totalCount = searchResults.size
            )
            
            return Response.success(response)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }
    
    // Helper methods
    private suspend fun simulateNetworkDelay() {
        if (AppConfig.MockConfig.NETWORK_DELAY_MIN > 0) {
            val delay = Random.nextLong(
                AppConfig.MockConfig.NETWORK_DELAY_MIN,
                AppConfig.MockConfig.NETWORK_DELAY_MAX
            )
            delay(delay)
        }
    }
    
    private fun extractUserIdFromToken(authToken: String): String? {
        val token = authToken.removePrefix("Bearer ").trim()
        return mockAuthApi.validateToken(token)
    }
    
    private fun <T> createErrorResponse(code: Int, message: String): Response<T> {
        return Response.error(
            code,
            okhttp3.ResponseBody.create(
                okhttp3.MediaType.parse("application/json"),
                """{"error": "$message", "status": $code}"""
            )
        )
    }
}
