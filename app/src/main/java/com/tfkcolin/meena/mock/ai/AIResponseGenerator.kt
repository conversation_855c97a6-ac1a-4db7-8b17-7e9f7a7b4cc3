package com.tfkcolin.meena.mock.ai

import com.tfkcolin.meena.config.AppConfig
import kotlinx.coroutines.delay
import kotlin.random.Random

/**
 * Generates AI responses based on personas and message context.
 */
class AIResponseGenerator {
    
    private val personas = AIPersonaFactory.getAllPersonas()
    private val conversationHistory = mutableMapOf<String, MutableList<ConversationMessage>>()
    
    data class ConversationMessage(
        val content: String,
        val isFromUser: Boolean,
        val timestamp: Long = System.currentTimeMillis()
    )
    
    /**
     * Generate a response to a user message.
     */
    suspend fun generateResponse(
        userMessage: String,
        chatId: String,
        personaId: String? = null,
        isFirstMessage: Boolean = false
    ): AIResponse {
        // Add user message to conversation history
        addToHistory(chatId, ConversationMessage(userMessage, true))
        
        // Get persona (random if not specified)
        val persona = personaId?.let { AIPersonaFactory.getPersonaById(it) } 
            ?: AIPersonaFactory.getRandomPersona()
        
        // Simulate thinking delay
        val delay = Random.nextLong(
            AppConfig.MockConfig.AI_RESPONSE_DELAY_MIN,
            AppConfig.MockConfig.AI_RESPONSE_DELAY_MAX
        )
        delay(delay)
        
        // Determine message trigger
        val trigger = determineTrigger(userMessage, isFirstMessage)
        
        // Generate response based on persona and trigger
        val response = generateResponseText(persona, trigger, userMessage, chatId)
        
        // Add AI response to conversation history
        addToHistory(chatId, ConversationMessage(response, false))
        
        return AIResponse(
            text = response,
            persona = persona,
            trigger = trigger,
            confidence = calculateConfidence(trigger, persona),
            shouldShowTyping = Random.nextFloat() < 0.8f, // 80% chance of showing typing
            typingDuration = Random.nextLong(1000, 3000)
        )
    }
    
    /**
     * Determine the appropriate trigger for the message.
     */
    private fun determineTrigger(message: String, isFirstMessage: Boolean): MessageTrigger {
        val lowerMessage = message.lowercase()
        
        return when {
            isFirstMessage -> MessageTrigger.FirstMessage
            
            // Greetings
            lowerMessage.contains(Regex("\\b(hi|hello|hey|good morning|good afternoon|good evening|greetings)\\b")) -> 
                MessageTrigger.Greeting
            
            // Questions
            lowerMessage.contains("?") || lowerMessage.startsWith(Regex("\\b(what|how|why|when|where|who|can|could|would|should|do|does|did|is|are|was|were)\\b")) -> 
                MessageTrigger.Question
            
            // Goodbyes
            lowerMessage.contains(Regex("\\b(bye|goodbye|see you|talk later|gotta go|take care|farewell)\\b")) -> 
                MessageTrigger.Goodbye
            
            // Compliments
            lowerMessage.contains(Regex("\\b(great|awesome|amazing|fantastic|wonderful|excellent|perfect|love|like|good job|well done)\\b")) -> 
                MessageTrigger.Compliment
            
            // Complaints
            lowerMessage.contains(Regex("\\b(bad|terrible|awful|hate|annoying|frustrated|angry|upset|disappointed)\\b")) -> 
                MessageTrigger.Complaint
            
            // Tech keywords
            lowerMessage.contains(Regex("\\b(code|programming|tech|app|software|ai|machine learning|computer|development|bug|feature)\\b")) -> 
                MessageTrigger.Keyword(listOf("tech"))
            
            // Default to random
            else -> MessageTrigger.Random
        }
    }
    
    /**
     * Generate response text based on persona and trigger.
     */
    private fun generateResponseText(
        persona: AIPersona,
        trigger: MessageTrigger,
        userMessage: String,
        chatId: String
    ): String {
        // Find matching response patterns
        val matchingPatterns = persona.responsePatterns.filter { pattern ->
            when {
                pattern.trigger::class == trigger::class -> true
                pattern.trigger is MessageTrigger.Keyword && trigger is MessageTrigger.Keyword -> {
                    pattern.trigger.keywords.any { keyword ->
                        userMessage.lowercase().contains(keyword.lowercase())
                    }
                }
                else -> false
            }
        }
        
        // If no specific pattern matches, use random responses
        val patterns = matchingPatterns.ifEmpty {
            persona.responsePatterns.filter { it.trigger is MessageTrigger.Random }
        }
        
        if (patterns.isEmpty()) {
            return generateFallbackResponse(persona, userMessage)
        }
        
        // Select a pattern based on probability
        val selectedPattern = patterns.filter { Random.nextFloat() < it.probability }.randomOrNull()
            ?: patterns.random()
        
        // Get base response
        var response = selectedPattern.responses.random()
        
        // Apply personality modifications
        response = applyPersonalityModifications(response, persona, userMessage)
        
        // Add context awareness
        response = addContextualElements(response, chatId, persona)
        
        return response
    }
    
    /**
     * Apply personality traits to modify the response.
     */
    private fun applyPersonalityModifications(
        response: String,
        persona: AIPersona,
        userMessage: String
    ): String {
        var modifiedResponse = response
        
        // Add enthusiasm
        if (persona.personality.enthusiasm > 0.7f && Random.nextFloat() < persona.personality.enthusiasm) {
            val enthusiasticWords = listOf("really", "absolutely", "definitely", "totally", "completely")
            if (Random.nextBoolean()) {
                modifiedResponse = "${enthusiasticWords.random()} $modifiedResponse"
            }
        }
        
        // Add humor
        if (persona.personality.humor > 0.6f && Random.nextFloat() < persona.personality.humor * 0.3f) {
            val humorousAdditions = listOf(" 😄", " 😊", " 😉", " (just kidding!)", " (or so I've heard!)")
            modifiedResponse += humorousAdditions.random()
        }
        
        // Add formality
        if (persona.personality.formality > 0.7f) {
            modifiedResponse = modifiedResponse.replace(Regex("\\b(hey|hi)\\b"), "Hello")
            modifiedResponse = modifiedResponse.replace(Regex("\\b(yeah|yep)\\b"), "Yes")
            modifiedResponse = modifiedResponse.replace(Regex("\\b(nah|nope)\\b"), "No")
        }
        
        // Add chattiness (extend response)
        if (persona.personality.chattiness > 0.7f && Random.nextFloat() < persona.personality.chattiness * 0.4f) {
            val chattyAdditions = listOf(
                " What do you think?",
                " How about you?",
                " Tell me more!",
                " I'd love to hear your thoughts.",
                " What's your experience with that?"
            )
            modifiedResponse += chattyAdditions.random()
        }
        
        return modifiedResponse
    }
    
    /**
     * Add contextual elements based on conversation history.
     */
    private fun addContextualElements(
        response: String,
        chatId: String,
        persona: AIPersona
    ): String {
        val history = conversationHistory[chatId] ?: return response
        
        // If this is a follow-up in a conversation
        if (history.size > 2 && Random.nextFloat() < 0.3f) {
            val contextualPrefixes = listOf(
                "Speaking of that, ",
                "That reminds me, ",
                "On a related note, ",
                "Actually, ",
                "You know what, "
            )
            return "${contextualPrefixes.random()}$response"
        }
        
        return response
    }
    
    /**
     * Generate a fallback response when no patterns match.
     */
    private fun generateFallbackResponse(persona: AIPersona, userMessage: String): String {
        val fallbackResponses = listOf(
            "That's interesting!",
            "I see what you mean.",
            "Thanks for sharing that.",
            "That's a good point.",
            "I understand.",
            "That makes sense.",
            "Interesting perspective!",
            "I appreciate you telling me that."
        )
        
        return fallbackResponses.random()
    }
    
    /**
     * Calculate confidence score for the response.
     */
    private fun calculateConfidence(trigger: MessageTrigger, persona: AIPersona): Float {
        return when (trigger) {
            is MessageTrigger.Greeting -> 0.9f
            is MessageTrigger.Goodbye -> 0.9f
            is MessageTrigger.Question -> 0.7f
            is MessageTrigger.Keyword -> 0.8f
            is MessageTrigger.FirstMessage -> 0.95f
            else -> 0.6f
        }
    }
    
    /**
     * Add message to conversation history.
     */
    private fun addToHistory(chatId: String, message: ConversationMessage) {
        val history = conversationHistory.getOrPut(chatId) { mutableListOf() }
        history.add(message)
        
        // Keep only last 20 messages to prevent memory issues
        if (history.size > 20) {
            history.removeAt(0)
        }
    }
    
    /**
     * Clear conversation history for a chat.
     */
    fun clearHistory(chatId: String) {
        conversationHistory.remove(chatId)
    }
    
    /**
     * Get conversation history for a chat.
     */
    fun getHistory(chatId: String): List<ConversationMessage> {
        return conversationHistory[chatId]?.toList() ?: emptyList()
    }
}

/**
 * AI response data class.
 */
data class AIResponse(
    val text: String,
    val persona: AIPersona,
    val trigger: MessageTrigger,
    val confidence: Float,
    val shouldShowTyping: Boolean = true,
    val typingDuration: Long = 2000L
)
