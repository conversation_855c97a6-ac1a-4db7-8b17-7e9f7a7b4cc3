package com.tfkcolin.meena.mock.api

import com.tfkcolin.meena.config.AppConfig
import com.tfkcolin.meena.data.api.AuthApi
import com.tfkcolin.meena.data.models.*
import com.tfkcolin.meena.mock.data.MockDataGenerator
import com.tfkcolin.meena.mock.storage.MockDataStorage
import kotlinx.coroutines.delay
import retrofit2.Response
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.random.Random

/**
 * Mock implementation of AuthApi for testing and development.
 */
@Singleton
class MockAuthApi @Inject constructor(
    private val mockDataStorage: MockDataStorage
) : AuthApi {
    
    private val activeTokens = mutableMapOf<String, String>() // accessToken -> userId
    private val refreshTokens = mutableMapOf<String, String>() // refreshToken -> userId
    
    override suspend fun register(request: RegisterRequest): Response<AuthResponse> {
        simulateNetworkDelay()
        
        try {
            // Validate request
            if (request.userHandle.isBlank() || request.password.isBlank()) {
                return createErrorResponse(400, "User handle and password are required")
            }
            
            // Check if user handle already exists
            val existingUsers = mockDataStorage.getUsers()
            if (existingUsers.values.any { it.userHandle == request.userHandle }) {
                return createErrorResponse(409, "User handle already exists")
            }
            
            // Generate new user
            val newUser = User(
                id = UUID.randomUUID().toString(),
                userHandle = request.userHandle,
                displayName = request.displayName ?: request.userHandle,
                bio = "",
                avatarUrl = "https://i.pravatar.cc/150?u=${UUID.randomUUID()}",
                email = request.email,
                phoneNumber = request.phoneNumber,
                isVerified = false,
                isGoldMember = false,
                verificationStatus = "none",
                lastActive = System.currentTimeMillis(),
                createdAt = System.currentTimeMillis(),
                followerCount = 0,
                followingCount = 0
            )
            
            // Save user
            mockDataStorage.addUser(newUser)
            
            // Generate tokens
            val accessToken = generateAccessToken(newUser.id)
            val refreshToken = generateRefreshToken(newUser.id)
            
            // Store tokens
            activeTokens[accessToken] = newUser.id
            refreshTokens[refreshToken] = newUser.id
            
            // Generate recovery phrase
            val recoveryPhrase = MockDataGenerator.generateRecoveryPhrase().joinToString(" ")
            
            val authResponse = AuthResponse(
                user = AuthUserProfile(
                    id = newUser.id,
                    userHandle = newUser.userHandle,
                    displayName = newUser.displayName,
                    bio = newUser.bio,
                    avatarUrl = newUser.avatarUrl,
                    verificationStatus = newUser.verificationStatus,
                    isVerified = newUser.isVerified,
                    isGoldMember = newUser.isGoldMember,
                    lastActive = newUser.lastActive.toString(),
                    createdAt = newUser.createdAt.toString(),
                    followerCount = newUser.followerCount,
                    followingCount = newUser.followingCount
                ),
                accessToken = accessToken,
                refreshToken = refreshToken,
                expiresIn = 86400, // 24 hours
                recoveryPhrase = recoveryPhrase
            )
            
            return Response.success(authResponse)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }
    
    override suspend fun login(request: LoginRequest): Response<AuthResponse> {
        simulateNetworkDelay()
        
        try {
            // Find user by identifier (user handle, email, or phone)
            val users = mockDataStorage.getUsers()
            val user = users.values.find { 
                it.userHandle == request.identifier || 
                it.email == request.identifier || 
                it.phoneNumber == request.identifier 
            }
            
            if (user == null) {
                return createErrorResponse(401, "Invalid credentials")
            }
            
            // For mock purposes, accept any password for existing users
            // In real implementation, you'd verify the password hash
            
            // Generate tokens
            val accessToken = generateAccessToken(user.id)
            val refreshToken = generateRefreshToken(user.id)
            
            // Store tokens
            activeTokens[accessToken] = user.id
            refreshTokens[refreshToken] = user.id
            
            // Update last active
            val updatedUser = user.copy(lastActive = System.currentTimeMillis())
            mockDataStorage.updateUser(updatedUser)
            
            val authResponse = AuthResponse(
                user = AuthUserProfile(
                    id = user.id,
                    userHandle = user.userHandle,
                    displayName = user.displayName,
                    bio = user.bio,
                    avatarUrl = user.avatarUrl,
                    verificationStatus = user.verificationStatus,
                    isVerified = user.isVerified,
                    isGoldMember = user.isGoldMember,
                    lastActive = user.lastActive.toString(),
                    createdAt = user.createdAt.toString(),
                    followerCount = user.followerCount,
                    followingCount = user.followingCount
                ),
                accessToken = accessToken,
                refreshToken = refreshToken,
                expiresIn = 86400, // 24 hours
                requires2fa = Random.nextFloat() < 0.1f // 10% chance of requiring 2FA
            )
            
            return Response.success(authResponse)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }
    
    override suspend fun refreshToken(request: RefreshTokenRequest): Response<AuthResponse> {
        simulateNetworkDelay()
        
        try {
            val userId = refreshTokens[request.refreshToken]
            if (userId == null) {
                return createErrorResponse(401, "Invalid refresh token")
            }
            
            val user = mockDataStorage.getUser(userId)
            if (user == null) {
                return createErrorResponse(404, "User not found")
            }
            
            // Generate new tokens
            val newAccessToken = generateAccessToken(userId)
            val newRefreshToken = generateRefreshToken(userId)
            
            // Remove old tokens and store new ones
            refreshTokens.remove(request.refreshToken)
            activeTokens[newAccessToken] = userId
            refreshTokens[newRefreshToken] = userId
            
            val authResponse = AuthResponse(
                user = AuthUserProfile(
                    id = user.id,
                    userHandle = user.userHandle,
                    displayName = user.displayName,
                    bio = user.bio,
                    avatarUrl = user.avatarUrl,
                    verificationStatus = user.verificationStatus,
                    isVerified = user.isVerified,
                    isGoldMember = user.isGoldMember,
                    lastActive = user.lastActive.toString(),
                    createdAt = user.createdAt.toString(),
                    followerCount = user.followerCount,
                    followingCount = user.followingCount
                ),
                accessToken = newAccessToken,
                refreshToken = newRefreshToken,
                expiresIn = 86400
            )
            
            return Response.success(authResponse)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }
    
    override suspend fun logout(request: LogoutRequest): Response<Unit> {
        simulateNetworkDelay()
        
        try {
            // Remove tokens
            refreshTokens.remove(request.refreshToken)
            activeTokens.entries.removeAll { it.value == refreshTokens[request.refreshToken] }
            
            return Response.success(Unit)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }
    
    override suspend fun changePassword(request: ChangePasswordRequest): Response<Unit> {
        simulateNetworkDelay()
        
        // For mock purposes, always succeed if user is authenticated
        return Response.success(Unit)
    }
    
    override suspend fun initiateRecovery(request: InitiateRecoveryRequest): Response<RecoveryResponse> {
        simulateNetworkDelay()
        
        try {
            // Find user by handle
            val users = mockDataStorage.getUsers()
            val user = users.values.find { it.userHandle == request.userHandle }
            
            if (user == null) {
                return createErrorResponse(404, "User not found")
            }
            
            // For mock purposes, accept any recovery phrase and PIN
            // In real implementation, you'd verify these against stored values
            
            // Generate recovery token
            val recoveryToken = "recovery_${UUID.randomUUID()}"
            
            val response = RecoveryResponse(recoveryToken = recoveryToken)
            return Response.success(response)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }
    
    override suspend fun confirmRecovery(request: ConfirmRecoveryRequest): Response<AuthResponse> {
        simulateNetworkDelay()
        
        try {
            // For mock purposes, accept any recovery token that starts with "recovery_"
            if (!request.recoveryToken.startsWith("recovery_")) {
                return createErrorResponse(400, "Invalid recovery token")
            }
            
            // For demo, use the first user as the recovered user
            val users = mockDataStorage.getUsers()
            val user = users.values.firstOrNull()
                ?: return createErrorResponse(404, "User not found")
            
            // Generate new tokens
            val accessToken = generateAccessToken(user.id)
            val refreshToken = generateRefreshToken(user.id)
            
            // Store tokens
            activeTokens[accessToken] = user.id
            refreshTokens[refreshToken] = user.id
            
            val authResponse = AuthResponse(
                user = AuthUserProfile(
                    id = user.id,
                    userHandle = user.userHandle,
                    displayName = user.displayName,
                    bio = user.bio,
                    avatarUrl = user.avatarUrl,
                    verificationStatus = user.verificationStatus,
                    isVerified = user.isVerified,
                    isGoldMember = user.isGoldMember,
                    lastActive = user.lastActive.toString(),
                    createdAt = user.createdAt.toString(),
                    followerCount = user.followerCount,
                    followingCount = user.followingCount
                ),
                accessToken = accessToken,
                refreshToken = refreshToken,
                expiresIn = 86400
            )
            
            return Response.success(authResponse)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }
    
    // Helper methods
    private suspend fun simulateNetworkDelay() {
        if (AppConfig.MockConfig.NETWORK_DELAY_MIN > 0) {
            val delay = Random.nextLong(
                AppConfig.MockConfig.NETWORK_DELAY_MIN,
                AppConfig.MockConfig.NETWORK_DELAY_MAX
            )
            delay(delay)
        }
    }
    
    private fun generateAccessToken(userId: String): String {
        return "mock_access_token_${userId}_${System.currentTimeMillis()}"
    }
    
    private fun generateRefreshToken(userId: String): String {
        return "mock_refresh_token_${userId}_${System.currentTimeMillis()}"
    }
    
    private fun <T> createErrorResponse(code: Int, message: String): Response<T> {
        return Response.error(
            code,
            okhttp3.ResponseBody.create(
                okhttp3.MediaType.parse("application/json"),
                """{"error": "$message", "status": $code}"""
            )
        )
    }
    
    /**
     * Validate if a token is valid and return the associated user ID.
     */
    fun validateToken(token: String): String? {
        return activeTokens[token]
    }
    
    /**
     * Get current authenticated user from token.
     */
    fun getCurrentUserFromToken(token: String): User? {
        val userId = validateToken(token) ?: return null
        return mockDataStorage.getUser(userId)
    }
}
