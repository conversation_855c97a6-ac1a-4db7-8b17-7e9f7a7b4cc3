package com.tfkcolin.meena.mock.data

import com.tfkcolin.meena.data.models.*
import java.util.*
import kotlin.random.Random

/**
 * Generates realistic mock data for the application.
 */
object MockDataGenerator {
    
    private val firstNames = listOf(
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Sydney"
    )
    
    private val lastNames = listOf(
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"
    )
    
    private val bios = listOf(
        "Love traveling and photography 📸",
        "Coffee enthusiast ☕ | Tech lover 💻",
        "Fitness enthusiast 💪 | Healthy living",
        "Artist at heart 🎨 | Creative soul",
        "Music lover 🎵 | Concert goer",
        "Foodie 🍕 | Always hungry",
        "Book worm 📚 | Knowledge seeker",
        "Adventure seeker 🏔️ | Outdoor enthusiast",
        "Gamer 🎮 | Digital native",
        "Entrepreneur 💼 | Building the future"
    )
    
    private val messageTemplates = listOf(
        "Hey! How's your day going?",
        "Just saw the most amazing sunset! 🌅",
        "Working on something exciting today",
        "Coffee break time! ☕",
        "Have you seen the latest news?",
        "Planning something fun for the weekend",
        "Just finished a great workout 💪",
        "Reading an interesting book right now",
        "The weather is perfect today!",
        "Trying out a new recipe 👨‍🍳"
    )
    
    private val groupNames = listOf(
        "Study Group", "Weekend Warriors", "Coffee Lovers", "Tech Talk",
        "Fitness Buddies", "Book Club", "Travel Squad", "Foodies United",
        "Gaming Crew", "Music Enthusiasts", "Art Collective", "Startup Ideas",
        "Movie Night", "Hiking Group", "Photography Club", "Cooking Class"
    )
    
    private val channelNames = listOf(
        "Tech News", "Daily Updates", "Announcements", "General Discussion",
        "Random Thoughts", "Inspiration", "Motivation Monday", "Tips & Tricks",
        "Industry Insights", "Community Updates", "Events & Meetups", "Q&A",
        "Feedback", "Suggestions", "Help & Support", "Resources"
    )
    
    /**
     * Generate a mock user with realistic data.
     */
    fun generateMockUser(id: String? = null): User {
        val userId = id ?: UUID.randomUUID().toString()
        val firstName = firstNames.random()
        val lastName = lastNames.random()
        val userHandle = "${firstName.lowercase()}${lastName.lowercase()}${Random.nextInt(100, 999)}"
        
        return User(
            id = userId,
            userHandle = userHandle,
            displayName = "$firstName $lastName",
            bio = bios.random(),
            avatarUrl = "https://i.pravatar.cc/150?u=$userId",
            email = "$<EMAIL>",
            phoneNumber = "+1${Random.nextInt(100, 999)}${Random.nextInt(100, 999)}${Random.nextInt(1000, 9999)}",
            isVerified = Random.nextBoolean(),
            isGoldMember = Random.nextFloat() < 0.3f, // 30% chance of being gold member
            verificationStatus = if (Random.nextBoolean()) "verified" else "none",
            lastActive = System.currentTimeMillis() - Random.nextLong(0, 86400000), // Within last 24 hours
            createdAt = System.currentTimeMillis() - Random.nextLong(86400000, 31536000000), // 1 day to 1 year ago
            followerCount = Random.nextInt(0, 1000),
            followingCount = Random.nextInt(0, 500)
        )
    }
    
    /**
     * Generate a mock contact relationship.
     */
    fun generateMockContact(userId: String, contactUser: User): Contact {
        return Contact(
            id = UUID.randomUUID().toString(),
            userId = userId,
            contactUserId = contactUser.id,
            contactUserHandle = contactUser.userHandle,
            displayName = contactUser.displayName,
            customDisplayName = if (Random.nextFloat() < 0.3f) "${contactUser.displayName} (${listOf("Work", "School", "Gym", "Neighbor").random()})" else null,
            avatarUrl = contactUser.avatarUrl,
            relationship = "contact",
            isBlocked = false,
            isFavorite = Random.nextFloat() < 0.2f, // 20% chance of being favorite
            addedAt = System.currentTimeMillis() - Random.nextLong(0, 31536000000), // Within last year
            lastInteraction = System.currentTimeMillis() - Random.nextLong(0, 86400000) // Within last 24 hours
        )
    }
    
    /**
     * Generate a mock chat/conversation.
     */
    fun generateMockChat(
        id: String? = null,
        participants: List<String>,
        type: ConversationType = ConversationType.ONE_TO_ONE,
        name: String? = null
    ): Chat {
        val chatId = id ?: UUID.randomUUID().toString()
        val chatName = when (type) {
            ConversationType.GROUP -> name ?: groupNames.random()
            ConversationType.CHANNEL -> name ?: channelNames.random()
            ConversationType.ONE_TO_ONE -> null
        }
        
        return Chat(
            id = chatId,
            conversationType = type.value,
            privacyType = when (type) {
                ConversationType.ONE_TO_ONE -> null
                else -> listOf("public", "private", "secret").random()
            },
            participantIds = participants.joinToString(","),
            name = chatName,
            description = if (type != ConversationType.ONE_TO_ONE && Random.nextBoolean()) {
                "A great place to ${listOf("chat", "share ideas", "collaborate", "have fun", "learn together").random()}"
            } else null,
            avatarUrl = if (type != ConversationType.ONE_TO_ONE) "https://i.pravatar.cc/150?u=$chatId" else null,
            adminIds = if (type != ConversationType.ONE_TO_ONE) participants.take(Random.nextInt(1, minOf(3, participants.size))).joinToString(",") else null,
            createdBy = participants.first(),
            lastMessage = messageTemplates.random(),
            lastMessageTimestamp = System.currentTimeMillis() - Random.nextLong(0, 86400000),
            unreadCount = Random.nextInt(0, 10),
            isArchived = Random.nextFloat() < 0.1f, // 10% chance of being archived
            isMuted = Random.nextFloat() < 0.15f, // 15% chance of being muted
            isPinned = Random.nextFloat() < 0.2f, // 20% chance of being pinned
            createdAt = System.currentTimeMillis() - Random.nextLong(86400000, 31536000000),
            isEncrypted = Random.nextBoolean(),
            lastMessageSenderId = participants.random(),
            lastMessageType = listOf("text", "image", "video", "audio", "file").random()
        )
    }
    
    /**
     * Generate a mock message.
     */
    fun generateMockMessage(
        chatId: String,
        senderId: String,
        id: String? = null,
        replyToId: String? = null
    ): Message {
        val messageId = id ?: UUID.randomUUID().toString()
        val messageType = if (Random.nextFloat() < 0.8f) "text" else listOf("image", "video", "audio", "file").random()
        
        return Message(
            id = messageId,
            chatId = chatId,
            senderId = senderId,
            content = if (messageType == "text") messageTemplates.random() else null,
            messageType = messageType,
            timestamp = System.currentTimeMillis() - Random.nextLong(0, 86400000),
            isEdited = Random.nextFloat() < 0.1f, // 10% chance of being edited
            editedAt = if (Random.nextFloat() < 0.1f) System.currentTimeMillis() - Random.nextLong(0, 3600000) else null,
            replyToMessageId = replyToId,
            isDeleted = false,
            deletedAt = null,
            deliveryStatus = listOf("sent", "delivered", "read").random(),
            readBy = if (Random.nextBoolean()) listOf(senderId) else emptyList(),
            reactions = if (Random.nextFloat() < 0.3f) {
                mapOf("👍" to listOf(senderId), "❤️" to listOf(senderId))
            } else emptyMap(),
            mediaAttachments = if (messageType != "text") {
                listOf(
                    MediaAttachment(
                        id = UUID.randomUUID().toString(),
                        messageId = messageId,
                        fileName = "mock_file.${if (messageType == "image") "jpg" else "mp4"}",
                        fileSize = Random.nextLong(1000000, 10000000),
                        mimeType = when (messageType) {
                            "image" -> "image/jpeg"
                            "video" -> "video/mp4"
                            "audio" -> "audio/mp3"
                            else -> "application/octet-stream"
                        },
                        url = "https://picsum.photos/300/200?random=${Random.nextInt()}",
                        thumbnailUrl = if (messageType in listOf("image", "video")) "https://picsum.photos/150/100?random=${Random.nextInt()}" else null,
                        duration = if (messageType in listOf("video", "audio")) Random.nextLong(10000, 300000) else null,
                        uploadedAt = System.currentTimeMillis() - Random.nextLong(0, 3600000)
                    )
                )
            } else emptyList(),
            isEncrypted = Random.nextBoolean(),
            encryptionKeyId = if (Random.nextBoolean()) UUID.randomUUID().toString() else null
        )
    }
    
    /**
     * Generate recovery phrase words.
     */
    fun generateRecoveryPhrase(): List<String> {
        val words = listOf(
            "apple", "banana", "cherry", "dog", "elephant", "frog", "giraffe", "hippo", "iguana",
            "jungle", "kangaroo", "lion", "monkey", "nightingale", "octopus", "penguin", "quail", "rabbit"
        )
        return words.shuffled().take(12)
    }
    
    /**
     * Generate a random Meena ID (9 characters).
     */
    fun generateMeenaId(): String {
        val chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
        return (1..9).map { chars.random() }.joinToString("")
    }
}
