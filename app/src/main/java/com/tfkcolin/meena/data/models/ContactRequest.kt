package com.tfkcolin.meena.data.models

import com.google.gson.annotations.SerializedName

/**
 * Add contact request model for the /contacts endpoint.
 */
data class AddContactRequest(
    @SerializedName("user_handle")
    val userHandle: String,

    @SerializedName("display_name")
    val displayName: String? = null,

    @SerializedName("notes")
    val notes: String? = null
)

/**
 * Update contact request model for the /contacts/{contact_id} endpoint.
 */
data class UpdateContactRequest(
    @SerializedName("display_name")
    val displayName: String? = null,

    @SerializedName("notes")
    val notes: String? = null,

    @SerializedName("relationship")
    val relationship: String? = null
)
