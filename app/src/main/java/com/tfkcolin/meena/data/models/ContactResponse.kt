package com.tfkcolin.meena.data.models

import com.google.gson.annotations.SerializedName

/**
 * Contact list response model for the /contacts endpoint.
 */
data class ContactListResponse(
    @SerializedName("contacts")
    val contacts: List<Contact>,

    @SerializedName("total_count")
    val totalCount: Int
)

/**
 * Contact response model for a single contact.
 * This is used for API responses that don't return the full Contact model.
 */
data class ContactResponse(
    @SerializedName("id")
    val id: String,

    @SerializedName("user_id")
    val userId: String,

    @SerializedName("contact_id")
    val contactId: String,

    @SerializedName("display_name")
    val displayName: String?,

    @SerializedName("relationship")
    val relationship: String,

    @SerializedName("notes")
    val notes: String?,

    @SerializedName("created_at")
    val createdAt: String,

    @SerializedName("updated_at")
    val updatedAt: String,

    @SerializedName("user")
    val user: UserProfile?
) {
    /**
     * Convert this ContactResponse to a Contact model.
     *
     * @return The Contact model.
     */
    fun toContact(): Contact {
        return Contact(
            id = id,
            userId = userId,
            contactUserId = contactId,
            contactUserHandle = user?.userHandle ?: "",
            displayName = displayName ?: user?.displayName ?: "",
            customDisplayName = displayName,
            avatarUrl = user?.avatarUrl,
            relationship = relationship,
            isBlocked = relationship == "blocked",
            isFavorite = false,
            addedAt = System.currentTimeMillis(),
            lastInteraction = System.currentTimeMillis()
        )
    }
}

/**
 * Request model for adding a contact.
 */
data class AddContactRequest(
    @SerializedName("contact_user_handle")
    val contactUserHandle: String,

    @SerializedName("custom_display_name")
    val customDisplayName: String? = null
)

/**
 * Request model for updating a contact.
 */
data class UpdateContactRequest(
    @SerializedName("custom_display_name")
    val customDisplayName: String? = null,

    @SerializedName("is_favorite")
    val isFavorite: Boolean? = null
)

/**
 * Request model for blocking a contact.
 */
data class BlockContactRequest(
    @SerializedName("contact_user_handle")
    val contactUserHandle: String
)

/**
 * Response model for contact search.
 */
data class ContactSearchResponse(
    @SerializedName("results")
    val results: List<ContactSearchResult>,

    @SerializedName("total_count")
    val totalCount: Int
)

/**
 * Individual contact search result.
 */
data class ContactSearchResult(
    @SerializedName("user_id")
    val userId: String,

    @SerializedName("user_handle")
    val userHandle: String,

    @SerializedName("display_name")
    val displayName: String,

    @SerializedName("avatar_url")
    val avatarUrl: String?,

    @SerializedName("bio")
    val bio: String?,

    @SerializedName("is_verified")
    val isVerified: Boolean,

    @SerializedName("mutual_contacts_count")
    val mutualContactsCount: Int,

    @SerializedName("is_contact")
    val isContact: Boolean
)
