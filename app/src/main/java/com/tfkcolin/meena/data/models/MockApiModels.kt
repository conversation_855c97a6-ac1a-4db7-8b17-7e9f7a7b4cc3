package com.tfkcolin.meena.data.models

import com.google.gson.annotations.SerializedName

/**
 * Additional models needed for mock API compatibility.
 * These extend or replace existing models to match the mock API contracts.
 */

/**
 * Enhanced ChatListResponse for mock API.
 */
data class ChatListResponse(
    @SerializedName("chats")
    val chats: List<ChatResponse>,

    @SerializedName("total_count")
    val totalCount: Int,

    @SerializedName("has_more")
    val hasMore: Boolean = false
)

/**
 * Enhanced ChatResponse for mock API.
 */
data class ChatResponse(
    @SerializedName("id")
    val id: String,

    @SerializedName("conversation_type")
    val conversationType: String,

    @SerializedName("privacy_type")
    val privacyType: String? = null,

    @SerializedName("participant_ids")
    val participantIds: String,

    @SerializedName("name")
    val name: String? = null,

    @SerializedName("description")
    val description: String? = null,

    @SerializedName("avatar_url")
    val avatarUrl: String? = null,

    @SerializedName("admin_ids")
    val adminIds: String? = null,

    @SerializedName("created_by")
    val createdBy: String? = null,

    @SerializedName("last_message")
    val lastMessage: String? = null,

    @SerializedName("last_message_timestamp")
    val lastMessageTimestamp: Long? = null,

    @SerializedName("unread_count")
    val unreadCount: Int = 0,

    @SerializedName("is_archived")
    val isArchived: Boolean = false,

    @SerializedName("is_muted")
    val isMuted: Boolean = false,

    @SerializedName("is_pinned")
    val isPinned: Boolean = false,

    @SerializedName("muted_until")
    val mutedUntil: Long? = null,

    @SerializedName("created_at")
    val createdAt: Long,

    @SerializedName("is_encrypted")
    val isEncrypted: Boolean = false,

    @SerializedName("last_message_sender_id")
    val lastMessageSenderId: String? = null,

    @SerializedName("last_message_type")
    val lastMessageType: String? = null
)

/**
 * Enhanced MessageListResponse for mock API.
 */
data class MessageListResponse(
    @SerializedName("messages")
    val messages: List<MessageResponse>,

    @SerializedName("has_more")
    val hasMore: Boolean = false
)

/**
 * Enhanced MessageResponse for mock API.
 */
data class MessageResponse(
    @SerializedName("id")
    val id: String,

    @SerializedName("chat_id")
    val chatId: String,

    @SerializedName("sender_id")
    val senderId: String,

    @SerializedName("content")
    val content: String?,

    @SerializedName("message_type")
    val messageType: String = "text",

    @SerializedName("timestamp")
    val timestamp: Long,

    @SerializedName("is_edited")
    val isEdited: Boolean = false,

    @SerializedName("edited_at")
    val editedAt: Long? = null,

    @SerializedName("reply_to_message_id")
    val replyToMessageId: String? = null,

    @SerializedName("is_deleted")
    val isDeleted: Boolean = false,

    @SerializedName("deleted_at")
    val deletedAt: Long? = null,

    @SerializedName("delivery_status")
    val deliveryStatus: String = "sent",

    @SerializedName("read_by")
    val readBy: List<String> = emptyList(),

    @SerializedName("reactions")
    val reactions: Map<String, List<String>> = emptyMap(),

    @SerializedName("media_attachments")
    val mediaAttachments: List<MediaAttachment> = emptyList(),

    @SerializedName("is_encrypted")
    val isEncrypted: Boolean = false,

    @SerializedName("encryption_key_id")
    val encryptionKeyId: String? = null
)

/**
 * Enhanced SendMessageRequest for mock API.
 */
data class SendMessageRequest(
    @SerializedName("content")
    val content: String?,

    @SerializedName("message_type")
    val messageType: String? = "text",

    @SerializedName("reply_to_message_id")
    val replyToMessageId: String? = null,

    @SerializedName("media_attachments")
    val mediaAttachments: List<MediaAttachment>? = null
)

/**
 * Enhanced EditMessageRequest for mock API.
 */
data class EditMessageRequest(
    @SerializedName("content")
    val content: String
)

/**
 * MarkAsReadRequest for mock API.
 */
data class MarkAsReadRequest(
    @SerializedName("last_read_message_id")
    val lastReadMessageId: String? = null
)

/**
 * Enhanced ContactListResponse for mock API.
 */
data class ContactListResponse(
    @SerializedName("contacts")
    val contacts: List<ContactResponse>,

    @SerializedName("total_count")
    val totalCount: Int,

    @SerializedName("has_more")
    val hasMore: Boolean = false
)

/**
 * Enhanced ContactResponse for mock API.
 */
data class ContactResponse(
    @SerializedName("id")
    val id: String,

    @SerializedName("user_id")
    val userId: String,

    @SerializedName("contact_user_id")
    val contactUserId: String,

    @SerializedName("contact_user_handle")
    val contactUserHandle: String,

    @SerializedName("display_name")
    val displayName: String,

    @SerializedName("avatar_url")
    val avatarUrl: String?,

    @SerializedName("relationship")
    val relationship: String,

    @SerializedName("is_blocked")
    val isBlocked: Boolean,

    @SerializedName("is_favorite")
    val isFavorite: Boolean,

    @SerializedName("added_at")
    val addedAt: Long,

    @SerializedName("last_interaction")
    val lastInteraction: Long,

    @SerializedName("is_online")
    val isOnline: Boolean = false,

    @SerializedName("last_seen")
    val lastSeen: Long? = null,

    @SerializedName("mutual_contacts_count")
    val mutualContactsCount: Int = 0,

    @SerializedName("is_verified")
    val isVerified: Boolean = false,

    @SerializedName("bio")
    val bio: String? = null
)

/**
 * Enhanced AddContactRequest for mock API.
 */
data class AddContactRequest(
    @SerializedName("contact_user_handle")
    val contactUserHandle: String,

    @SerializedName("custom_display_name")
    val customDisplayName: String? = null
)

/**
 * Enhanced UpdateContactRequest for mock API.
 */
data class UpdateContactRequest(
    @SerializedName("custom_display_name")
    val customDisplayName: String? = null,

    @SerializedName("is_favorite")
    val isFavorite: Boolean? = null
)

/**
 * BlockContactRequest for mock API.
 */
data class BlockContactRequest(
    @SerializedName("contact_user_handle")
    val contactUserHandle: String
)

/**
 * ContactSearchResponse for mock API.
 */
data class ContactSearchResponse(
    @SerializedName("results")
    val results: List<ContactSearchResult>,

    @SerializedName("total_count")
    val totalCount: Int
)

/**
 * ContactSearchResult for mock API.
 */
data class ContactSearchResult(
    @SerializedName("user_id")
    val userId: String,

    @SerializedName("user_handle")
    val userHandle: String,

    @SerializedName("display_name")
    val displayName: String,

    @SerializedName("avatar_url")
    val avatarUrl: String?,

    @SerializedName("bio")
    val bio: String?,

    @SerializedName("is_verified")
    val isVerified: Boolean,

    @SerializedName("mutual_contacts_count")
    val mutualContactsCount: Int,

    @SerializedName("is_contact")
    val isContact: Boolean
)
